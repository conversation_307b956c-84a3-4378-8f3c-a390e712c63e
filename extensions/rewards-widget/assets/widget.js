// Instead use the window globals that we set up in widget.liquid
const { html, render } = window;

// Define SVG icons with themed colors
const ICONS = {
  BACK: html`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="color: var(--rewards-primary-color)">
      <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" fill="currentColor"/>
    </svg>
  `,

  STAR: html`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="color: var(--rewards-primary-color)">
      <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="currentColor"/>
    </svg>
  `,

  CLOSE: html`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="#fff"/>
    </svg>
  `,

  WALLET: html`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="color: var(--rewards-primary-color)">
      <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z" fill="currentColor"/>
    </svg>
  `,

  INFO: html`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="color: var(--rewards-primary-color)">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" fill="currentColor"/>
    </svg>
  `,

  COPY: html`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="color: var(--rewards-primary-color)">
      <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
    </svg>
  `,

  CHECK: html`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="color: var(--rewards-primary-color)">
      <circle cx="12" cy="12" r="12" fill="currentColor"/>
      <path d="M9.5 15.5L5.5 11.5L4 13L9.5 18.5L20.5 7.5L19 6L9.5 15.5Z" fill="white"/>
    </svg>
  `,

  HEART: html`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M12 21.35L10.55 20.03C5.4 15.36 2 12.27 2 8.5C2 5.41 4.42 3 7.5 3C9.22 3 10.95 3.86 12 5.3C13.05 3.86 14.78 3 16.5 3C19.58 3 22 5.41 22 8.5C22 12.27 18.6 15.36 13.45 20.03L12 21.35Z" fill="#fff"/>
    </svg>
  `,

  CHEVRON_RIGHT: html`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="color: var(--rewards-primary-color)">
      <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z" fill="currentColor"/>
    </svg>
  `,

  REDEEM: html`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="color: var(--rewards-primary-color)">
      <path d="M20 6h-2.18c.11-.31.18-.65.18-1 0-1.66-1.34-3-3-3-1.05 0-1.96.54-2.5 1.35l-.5.67-.5-.68C10.96 2.54 10.05 2 9 2 7.34 2 6 3.34 6 5c0 .35.07.69.18 1H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm9 15H4v-2h16v2zm0-5H4V8h5.08L7 10.83 8.62 12 12 7.4l3.38 4.6L17 10.83 14.92 8H20v6z" fill="currentColor"/>
    </svg>
  `,

  TIER: html`
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" style="color: var(--rewards-primary-color)">
      <path d="M12 2L15.5 7L21 8L17 12.5L18 18L12 15.5L6 18L7 12.5L3 8L8.5 7L12 2Z M7 21H17V19H7V21Z" fill="currentColor"/>
    </svg>
  `
};

// Add a helper for common button styles
const buttonStyles = `
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: white;
  border: none;
  border-radius: 12px;
  width: 100%;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: transform 0.2s ease;
  font-family: var(--rewards-font-family);
`;

// Add a helper for common header styles
const headerWithBackButton = (title, onBackClick) => html`
  <div style="
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
  ">
    <button @click=${onBackClick} style="
      background: none;
      border: none;
      padding: 0;
      cursor: pointer;
      display: flex;
      align-items: center;
    ">
      ${ICONS.BACK}
    </button>
    <h3 style="
      font-size: 24px;
      color: var(--rewards-text-color);
      font-weight: 700;
      margin: 0;
      font-family: var(--rewards-font-family);
    ">${title}</h3>
  </div>
`;

const loadingView = () => html`
  <div data-view="loading" style="
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    flex-direction: column;
    gap: 15px;
  ">
    <div class="loading-spinner" style="
      width: 40px;
      height: 40px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid var(--rewards-primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    "></div>
    <p style="
      color: var(--rewards-text-color);
      font-size: 16px;
      margin: 0;
      font-family: var(--rewards-font-family);
    ">Loading your rewards...</p>
  </div>
`;

const registrationView = () => html`
  <div data-view="registration">
    <h3 style="
      text-align: center;
      margin: 0 0 10px 0;
      font-size: 24px;
      color: var(--rewards-text-color);
      font-weight: 700;
      font-family: var(--rewards-font-family);
    ">How to Join?</h3>

    <p style="
      text-align: center;
      margin: 0 0 20px 0;
      color: var(--rewards-text-color);
      font-size: 16px;
      line-height: 1.5;
      font-family: var(--rewards-font-family);
    ">Welcome to ${window.shopData.programName}! Unlock exclusive rewards just for you.</p>

    <!-- Step 1 -->
    <div style="
      border: 1px solid var(--rewards-primary-color);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
      position: relative;
      display: flex;
      flex-direction: column;
      gap: 20px;
    " data-step="1">
      <div style="
        position: absolute;
        top: -12px;
        left: 20px;
        background: white;
        padding: 0 10px;
        color: var(--rewards-primary-color);
        font-family: var(--rewards-font-family);
        font-size: 18px;
      ">
        Step: 1
      </div>
      <h4 style="
        margin: 0;
        font-size: 20px;
        color: var(--rewards-text-color);
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: var(--rewards-font-family);
      ">
        Sign up for your ${window.shopData.name} account
        ${window.customerData?.id ? html`${ICONS.CHECK}` : ''}
      </h4>

      ${!window.customerData?.id ? html`
        <div style="
          display: flex;
          flex-direction: column;
          gap: 10px;
          align-items: center;
        ">
          <a href="/account/register"
            style="
              display: inline-block;
              width: 100%;
              padding: 15px;
              background: var(--rewards-primary-color);
              color: white;
              text-decoration: none;
              text-align: center;
              border-radius: 100px;
              font-size: 16px;
              font-weight: 500;
              font-family: var(--rewards-font-family);
              transition: opacity 0.2s ease;
            "
            onmouseover="this.style.opacity='0.9'"
            onmouseout="this.style.opacity='1'"
          >
            Join Now
          </a>

          <p style="
            margin: 0;
            font-size: 14px;
            color: var(--rewards-text-color);
            font-family: var(--rewards-font-family);
          ">
            Already have an account?
            <a href="/account/login"
              style="
                color: var(--rewards-primary-color);
                text-decoration: none;
                font-weight: 500;
              "
              onmouseover="this.style.opacity='0.8'"
              onmouseout="this.style.opacity='1'"
            >
              Sign in
            </a>
          </p>
        </div>
      ` : ''}
    </div>

    <!-- Step 2 -->
    ${window.customerData?.id ? html`
    <div style="
      border: 1px solid var(--rewards-primary-color);
      border-radius: 12px;
      padding: 20px;
      position: relative;
      width: 100%;
      box-sizing: border-box;
    " data-step="2">
      <div style="
        position: absolute;
        top: -12px;
        left: 20px;
        background: white;
        padding: 0 10px;
        color: var(--rewards-primary-color);
        font-size: 18px;
        font-family: var(--rewards-font-family);
      ">
        Step: 2
      </div>
      <h4 style="
        margin: 0 0 8px 0;
        font-size: 20px;
        color: var(--rewards-text-color);
        padding-right: 20px;
        font-family: var(--rewards-font-family);
      ">Share your mobile number to unlock exclusive rewards!</h4>
      <p style="
        margin: 0 0 15px 0;
        color: var(--rewards-text-color);
        font-size: 14px;
        line-height: 1.3;
        padding-right: 20px;
        font-family: var(--rewards-font-family);
      ">We use your mobile number to recognize you in-store, so you can earn points during your visits to our physical stores, too!</p>

      <div style="
        display: flex;
        flex-direction: column;
        gap: 15px;
        box-sizing: border-box;
      ">
        <div style="
          display: flex;
          gap: 10px;
        ">
          <select style="
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            color: var(--rewards-text-color);
            background-color: white;
            width: 90px;
            flex-shrink: 0;
            font-family: var(--rewards-font-family);
          ">
            <option value="+94">🇱🇰 +94</option>
          </select>
          <input type="tel"
            id="phone"
            placeholder="************"
            maxlength="12"
            onkeyup="formatPhoneNumber(this)"
            onkeypress="return onlyNumbers(event)"
            style="
              flex: 1;
              min-width: 0;
              padding: 12px;
              border: 1px solid #ddd;
              border-radius: 8px;
              font-size: 16px;
              color: var(--rewards-text-color);
              font-family: var(--rewards-font-family);
              max-width: 64%;
            "
          >
        </div>

        <p id="phone-error" style="
          color: #dc3545;
          font-size: 12px;
          margin: 0;
          display: none;
          font-family: var(--rewards-font-family);
        "></p>

        <button style="
          width: 100%;
          padding: 12px;
          background: var(--rewards-primary-color);
          color: white;
          border: none;
          border-radius: 100px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          font-family: var(--rewards-font-family);
        " onmouseover="this.style.opacity='0.9'"
          onmouseout="this.style.opacity='1'"
          onclick="validateAndSubmit()"
          id="register-btn"
        >
          Enter
        </button>
        <p style="
          margin: 15px 0 0 0;
          color: var(--rewards-text-color);
          font-size: 12px;
          line-height: 1.4;
          text-align: center;
          font-family: var(--rewards-font-family);
        ">If you have already signed up for the ${window.shopData.programName} at an outlet, please enter the same mobile number you used to sign up.</p>
      </div>
    </div>
    ` : html`
    <div style="
      border: 1px solid var(--rewards-primary-color);
      border-radius: 12px;
      padding: 20px;
      position: relative;
      width: 100%;
      box-sizing: border-box;
    " data-step="2">
      <div style="
        position: absolute;
        top: -12px;
        left: 20px;
        background: white;
        padding: 0 10px;
        color: var(--rewards-primary-color);
        font-size: 18px;
        font-family: var(--rewards-font-family);
      ">
        Step: 2
      </div>
      <h4 style="
        margin: 0 0 8px 0;
        font-size: 20px;
        color: var(--rewards-text-color);
        padding-right: 20px;
        font-family: var(--rewards-font-family);
      ">Share your mobile number to unlock exclusive rewards!</h4>
      <p style="
        margin: 0 0 15px 0;
        color: var(--rewards-text-color);
        font-size: 14px;
        line-height: 1.3;
        padding-right: 20px;
        font-family: var(--rewards-font-family);
      ">We use your mobile number to recognize you in-store, so you can earn points during your visits to our physical stores, too!</p>
    </div>
    `}
  </div>
`;

const profileView = (memberData, tiers, pointsName,freeShippingEligible) => {
  const { currentTier, nextTier } = getCurrentTierAndNextTier(tiers, memberData);

  // Load coupons in background if we have member data
  if (memberData?.cardNumber && !isCouponsLoading) {
    loadCouponsInBackground();
  }

  return html`
    <!-- Profile Header -->
    <div style="text-align: center; margin-bottom: 30px;" data-view="profile">
      <h4 style="
        margin: 0 0 5px 0;
        font-size: 16px;
        color: #666;
        font-family: var(--rewards-font-family);
      ">Available ${pointsName}</h4>
      <h2 style="
        margin: 0;
        font-size: 42px;
        font-weight: 700;
        color: #333;
        font-family: var(--rewards-font-family);
      ">${memberData?.allowedRedeemablePoints || 0}</h2>

      ${tiers && tiers.length > 0 ? html`
        <div style="
          border: 1px solid var(--rewards-primary-color);
          border-radius: 100px;
          padding: 6px 16px;
          color: var(--rewards-primary-color);
          font-size: 14px;
          font-family: var(--rewards-font-family);
          display: inline-flex;
          align-items: center;
          gap: 4px;
          margin-top: 10px;
        ">
          ${currentTier?.imageUrl ? html`
            <img src="${currentTier.imageUrl}"
                 alt="${currentTier.name}"
                 width="32"
                 height="32"
                 style="object-fit: contain;"
                 @error=${(e) => {
          e.target.style.display = 'none';
          e.target.nextElementSibling.style.display = 'block';
        }}
            />
            <div style="display: none;">${ICONS.TIER}</div>
          ` : html`${ICONS.TIER}`}
          ${currentTier?.name ?? 'Unknown'}
        </div>
      ` : ''}
      ${freeShippingEligible ? html`
        <div style="
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 4px;
            width: 100%;
            margin-bottom: 10px;
          ">
          <p style="
              margin: 0 0 0 36px;
              font-size: 14px;
              color: #666;
              font-family: var(--rewards-font-family);
            ">Enjoy free shipping with your tier</p>
          <a
            @click=${() => window.showCouponHistory()}
            style="
                margin: 0 0 0 36px;
                font-size: 14px;
                color: var(--rewards-primary-color);
                text-decoration: underline;
                cursor: pointer;
                font-family: var(--rewards-font-family);
              "
            onmouseover="this.style.opacity='0.8'"
            onmouseout="this.style.opacity='1'"
          >Claim now</a>
        </div>
      ` : ''}
    </div>

    <!-- Menu Items -->
    <div style="display: flex; flex-direction: column; gap: 10px;">
      <!-- Your Coupons Button (Moved to first position) -->
      <button
        @click=${() => window.showCouponHistory()}
        style=${buttonStyles}
        @mouseover=${(e) => e.target.style.transform = 'translateY(-2px)'}
        @mouseout=${(e) => e.target.style.transform = 'translateY(0)'}
      >
        <div style="
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;
          width: 100%;
        ">
          <div style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
          ">
            <div style="display: flex; align-items: center; gap: 12px;">
              ${ICONS.REDEEM}
              <span style="font-size: 18px; color: #333; text-align: left;">Your Coupons</span>
            </div>
            ${ICONS.CHEVRON_RIGHT}
          </div>
          <p style="
            margin: 0 0 0 36px;
            font-size: 14px;
            color: #666;
          ">You've got ${availableCoupons.length} coupons ready to apply</p>
        </div>
      </button>

      <!-- Other buttons -->
      <button
        @click=${() => window.showEarnPoints()}
        style=${buttonStyles}
        @mouseover=${(e) => e.target.style.transform = 'translateY(-2px)'}
        @mouseout=${(e) => e.target.style.transform = 'translateY(0)'}
      >
        <div style="display: flex; align-items: center; gap: 12px;">
          ${ICONS.WALLET}
          <span style="font-size: 18px; color: #333; text-align: left;">Earn ${pointsName}</span>
        </div>
        ${ICONS.CHEVRON_RIGHT}
      </button>

      <button
        @click=${() => window.showRedeemPoints()}
        style=${buttonStyles}
        @mouseover=${(e) => e.target.style.transform = 'translateY(-2px)'}
        @mouseout=${(e) => e.target.style.transform = 'translateY(0)'}
      >
        <div style="display: flex; align-items: center; gap: 12px;">
          ${ICONS.REDEEM}
          <span style="font-size: 18px; color: #333; text-align: left;">Redeem ${pointsName}</span>
        </div>
        ${ICONS.CHEVRON_RIGHT}
      </button>

      ${tiers && tiers.length > 0 ? html`
        <button
          @click=${() => window.showTierBenefits()}
          style=${buttonStyles}
          @mouseover=${(e) => e.target.style.transform = 'translateY(-2px)'}
          @mouseout=${(e) => e.target.style.transform = 'translateY(0)'}
        >
          <div style="display: flex; align-items: center; gap: 12px;">
            ${ICONS.TIER}
            <span style="font-size: 18px; color: #333; text-align: left;">Tier Benefits</span>
          </div>
          ${ICONS.CHEVRON_RIGHT}
        </button>
      ` : ''}

      <button
        @click=${() => window.showAbout()}
        style=${buttonStyles}
        @mouseover=${(e) => e.target.style.transform = 'translateY(-2px)'}
        @mouseout=${(e) => e.target.style.transform = 'translateY(0)'}
      >
        <div style="display: flex; align-items: center; gap: 12px;">
          ${ICONS.INFO}
          <span style="font-size: 18px; color: #333; text-align: left;">About ${window.shopData.programName}</span>
        </div>
        ${ICONS.CHEVRON_RIGHT}
      </button>
    </div>
  `;
};

const earnPointsView = (generalSpendingRule) => {
  // Function to generate the points description based on the rule type
  const generatePointsDescription = () => {
    if (!generalSpendingRule || !generalSpendingRule.ruleData) {
      // Fallback to default if no rule data is available
      return `1 ${window.shopData.pointsName} for every ${window.shopData.currency} 100 spent`;
    }

    const {ruleData} = generalSpendingRule;

    if (ruleData.pointAmountsForRangesEnabled) {
      // Handle tiered point system
      if (ruleData.pointAmountMappingsForRanges && ruleData.pointAmountMappingsForRanges.length > 0) {
        // Sort ranges from highest to lowest for display
        const sortedRanges = [...ruleData.pointAmountMappingsForRanges].sort((a, b) =>
          b.billValueMargin - a.billValueMargin
        );

        return html`
          <div style="display: flex; flex-direction: column;">
            ${sortedRanges.map((range, index) => {
              const nextRange = sortedRanges[index + 1];
              const previousRange = sortedRanges[index - 1];
              let rangeText;

              if (index === 0 && range.billValueMargin > 0) {
                rangeText = `For orders of ${window.shopData.currency||""}  ${range.billValueMargin} and above`;
              } else if (nextRange) {
                rangeText = `For orders between ${window.shopData.currency||""} ${range?.billValueMargin||0} - ${window.shopData.currency} ${previousRange.billValueMargin-1||0}`;
              } else {
                rangeText = `For orders up to ${window.shopData.currency||""} ${previousRange.billValueMargin-1||0}`;
              }
              return html`
                <div style="margin: 0 0 20px 0; border-bottom: 1px solid #dadada; padding-bottom: 10px;">
                  ${rangeText} - 1 ${window.shopData.pointsName} for every ${window.shopData.currency}
                  ${range.amountPerPoint} spent
                </div>
              `
            })}
          </div>
        `

      }
    } else {
      // Handle flat rate system
      const amountPerPoint = ruleData.amountPerPoint || 100; // Default to 100 if not specified
      return `1 ${window.shopData.pointsName} for every ${window.shopData.currency} ${amountPerPoint} spent`;
    }

    // Fallback
    return `1 ${window.shopData.pointsName} for every ${window.shopData.currency} 100 spent`;
  };

  return html`
    ${headerWithBackButton(`How to Earn ${window.shopData.pointsName}`, () => showProfileView())}
    <p style="
    color: #666;
    font-size: 16px;
    line-height: 1.5;
    margin: 0 0 20px 0;
  ">Earn more ${window.shopData.pointsName} for every action, and turn your ${window.shopData.pointsName} into exciting
      rewards!</p>

    <div style="display: flex; flex-direction: column; gap: 15px;">
      <div style="
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    ">
        <div style="display: flex; gap: 15px; align-items: start;">
          ${ICONS.WALLET}
          <div>
            <h4 style="
            margin: 0 0 5px 0;
            font-size: 18px;
            color: #333;
            font-family: var(--rewards-font-family);
            font-weight: 500;
          ">Place an order</h4>
            <div style="
            margin: 0;
            color: #666;
            font-size: 16px;
            font-family: var(--rewards-font-family);
          ">${generatePointsDescription()}
            </div>
          </div>
        </div>
      </div>
    </div>
  `
}
// Add near the top with other state variables
let selectedPoints = 100; // Default value
let popupVisible = false;
let isInitialized = false;
let memberDataRefreshInterval = null;

// Add these near other state variables
let availableCoupons = [];
let isCouponsLoading = false;
let lastCouponLoadTime = 0;
const COUPON_REFRESH_INTERVAL = 5 * 60 * 1000; // 5 minutes
const PRIMARY_KEY = "MOBILE_NUMBER";

// Update loadCouponsInBackground to include caching
async function loadCouponsInBackground(forceRefresh = false) {
  // Return if already loading
  if (isCouponsLoading) return;

  // Check if we need to refresh
  const now = Date.now();
  if (!forceRefresh && lastCouponLoadTime && (now - lastCouponLoadTime < COUPON_REFRESH_INTERVAL)) {
    return; // Use cached data if within refresh interval
  }

  try {
    isCouponsLoading = true;
    const coupons = await fetchCouponHistory() ?? [];
    const newAvailableCoupons = coupons.filter(coupon =>
      coupon.status === 'ACTIVE' &&
      (!coupon.usageLimit || coupon.timesUsed < coupon.usageLimit) &&
      (!coupon.endsAt || new Date(coupon.endsAt) >= new Date())
    );

    // Update cache time and coupons
    lastCouponLoadTime = now;
    availableCoupons = newAvailableCoupons;

    // Re-render profile view if needed
    const contentSection = document.querySelector('.rewards-panel > div:last-child');
    if (contentSection && contentSection.getAttribute('data-view') === 'profile') {
      renderView(
        profileView(getMemberData(), getTiers(), window.shopData.pointsName,getFreeShippingEligible()),
        contentSection
      );
    } else if (contentSection && contentSection.getAttribute('data-view') === 'coupon-history') {
      renderView(
        couponHistoryView(availableCoupons,getFreeShippingEligible()),
        contentSection
      );
    }
  } catch (error) {
    console.error('Error loading coupons:', error);
    availableCoupons = [];
  } finally {
    isCouponsLoading = false;
  }
}

// Add a function to create a reactive redeem points view
function createRedeemPointsView(availablePoints) {
  const pointConfig = getPointConfiguration();
  const minPoints = Math.max(pointConfig?.minPointRedemptionAmount ?? 1, 1);
  const maxPoints = Math.min(pointConfig?.maxPointRedemptionAmount ?? 1000000, availablePoints);
  selectedPoints = minPoints;

  // Add custom slider styles
  const sliderStyles = document.createElement('style');
  sliderStyles.textContent = `
    input[type="range"] {
      -webkit-appearance: none;
      appearance: none;
      background: transparent;
      cursor: pointer;
    }

    /* Track styles */
    input[type="range"]::-webkit-slider-runnable-track {
      background: #E5E7EB;
      height: 6px;
      border-radius: 3px;
    }

    input[type="range"]::-moz-range-track {
      background: #E5E7EB;
      height: 6px;
      border-radius: 3px;
    }

    /* Thumb styles */
    input[type="range"]::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      margin-top: -6px;
      background-color: var(--rewards-primary-color);
      height: 18px;
      width: 18px;
      border-radius: 50%;
      border: none;
    }

    input[type="range"]::-moz-range-thumb {
      border: none;
      background-color: var(--rewards-primary-color);
      height: 18px;
      width: 18px;
      border-radius: 50%;
    }

    /* Focus styles */
    input[type="range"]:focus {
      outline: none;
    }

    input[type="range"]:focus::-webkit-slider-thumb {
      box-shadow: 0 0 0 3px rgba(var(--rewards-primary-color-rgb), 0.2);
    }

    input[type="range"]:focus::-moz-range-thumb {
      box-shadow: 0 0 0 3px rgba(var(--rewards-primary-color-rgb), 0.2);
    }

    /* Progress bar effect */
    input[type="range"] {
      background: linear-gradient(
        to right,
        var(--rewards-primary-color) 0%,
        var(--rewards-primary-color) calc((${selectedPoints} - ${minPoints}) / (${maxPoints} - ${minPoints}) * 100%),
        #E5E7EB calc((${selectedPoints} - ${minPoints}) / (${maxPoints} - ${minPoints}) * 100%),
        #E5E7EB 100%
      );
      height: 6px;
      border-radius: 3px;
    }
  `;
  document.head.appendChild(sliderStyles);

  const renderRedeemView = (points) => {
    // Update the progress bar when points change
    sliderStyles.textContent = sliderStyles.textContent.replace(
      /linear-gradient\([^)]+\)/,
      `linear-gradient(
        to right,
        var(--rewards-primary-color) 0%,
        var(--rewards-primary-color) calc((${points} - ${minPoints}) / (${maxPoints} - ${minPoints}) * 100%),
        #E5E7EB calc((${points} - ${minPoints}) / (${maxPoints} - ${minPoints}) * 100%),
        #E5E7EB 100%
      )`
    );

    return html`
      ${headerWithBackButton(`Redeem ${window.shopData.pointsName}`, () => showProfileView())}

      <div style="text-align: center; margin-bottom: 40px;">
        <h4 style="
          margin: 0 0 5px 0;
          font-size: 16px;
          color: #666;
          font-family: var(--rewards-font-family);
        ">Available ${window.shopData.pointsName}</h4>
        <h2 style="
          margin: 0;
          font-size: 42px;
          font-weight: 700;
          color: #333;
          font-family: var(--rewards-font-family);
        ">${availablePoints}</h2>
      </div>

      <div style="
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        margin-bottom: 20px;
      ">
        <div style="margin-bottom: 30px;">
          <label style="
            display: block;
            margin-bottom: 15px;
            font-size: 16px;
            color: var(--rewards-text-color);
            font-family: var(--rewards-font-family);
          ">Select amount to redeem</label>

          <input type="range"
            min="${minPoints}"
            max="${maxPoints}"
            step="1"
            value="${points}"
            @input=${(e) => {
        selectedPoints = parseInt(e.target.value);
        const contentSection = document.querySelector('.rewards-panel > div:last-child');
        render(renderRedeemView(selectedPoints), contentSection);
      }}
            style="width: 100%; margin: 10px 0;"
          >

          <div style="
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #666;
            font-family: var(--rewards-font-family);
          ">
            <span>${minPoints}</span>
            <span>${maxPoints}</span>
          </div>

          <div style="
            text-align: center;
            font-size: 24px;
            color: var(--rewards-text-color);
            font-weight: 500;
            margin-top: 15px;
            font-family: var(--rewards-font-family);
          ">
            ${points} ${window.shopData.pointsName}
          </div>
        </div>

        <button
          id="redeem-points-button"
          @click=${() => handleRedemption(points)}
          style="
            width: 100%;
            padding: 15px;
            background: var(--rewards-primary-color);
            color: white;
            border: none;
            border-radius: 100px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            font-family: var(--rewards-font-family);
          "
        >
          Redeem ${points} ${window.shopData.pointsName}
        </button>
      </div>
    `;
  };

  return renderRedeemView(selectedPoints);
}

// Update the redeemPointsView to use the new reactive view
const redeemPointsView = (availablePoints) => createRedeemPointsView(availablePoints);

const tierBenefitsView = (tiers, currentTier) => html`
  ${headerWithBackButton('Tier Benefits', () => showProfileView())}

  ${tiers.sort((a, b) => a.points - b.points).map(tier => html`
    <div style="
      background: white;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      ${currentTier?._id === tier._id ? 'border: 2px solid var(--rewards-primary-color);' : ''}
    ">
      <div style="
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
      ">
        <div style="
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          ${tier.imageUrl ? html`
            <img src="${tier.imageUrl}"
                 alt="${tier.name}"
                 width="32"
                 height="32"
                 style="object-fit: contain;"
                 @error=${(e) => {
      e.target.style.display = 'none';
      e.target.nextElementSibling.style.display = 'block';
    }}
            />
            <div style="display: none;">${ICONS.TIER}</div>
          ` : html`${ICONS.TIER}`}
        </div>
        <div>
          <h4 style="
            margin: 0;
            font-size: 20px;
            color: var(--rewards-text-color);
            font-family: var(--rewards-font-family);
          ">${tier.name}</h4>
          <p style="
            margin: 4px 0 0 0;
            font-size: 14px;
            color: var(--rewards-text-color);
            opacity: 0.7;
            font-family: var(--rewards-font-family);
          ">${tier.points} points required</p>
        </div>
        ${currentTier?._id === tier._id ? html`
          <div style="
            margin-left: auto;
            background: var(--rewards-primary-color);
            color: white;
            padding: 4px 12px;
            border-radius: 100px;
            font-size: 12px;
            font-family: var(--rewards-font-family);
          ">Current Tier</div>
        ` : ''}
      </div>

      ${tier.benefits?.length ? html`
        <ul style="
          margin: 0;
          padding-left: 20px;
          color: var(--rewards-text-color);
          font-family: var(--rewards-font-family);
        ">
          ${tier.benefits.map(benefit => html`
            <li style="margin-bottom: 8px;">${benefit}</li>
          `)}
        </ul>
      ` : html`
        <p style="
          margin: 0;
          color: var(--rewards-text-color);
          font-family: var(--rewards-font-family);
        ">No benefits available for this tier.</p>
      `}
    </div>
  `)}
`;

// Render function to update content
function renderView(view, container) {
  render(view, container);
}

// Add near the top of the file with other state variables
let customerDataPoller = null;
let refreshInterval = null;

// Add these functions
function startAutoRefresh() {
  if (refreshInterval) clearInterval(refreshInterval);
  refreshInterval = setInterval(loadWidgetData, 15000); // 15 seconds
}

function stopAutoRefresh() {
  if (refreshInterval) {
    clearInterval(refreshInterval);
    refreshInterval = null;
  }
}

// Add a helper function to render icon to HTML string
function renderIconToString(iconTemplate) {
  const div = document.createElement('div');
  render(iconTemplate, div);
  return div.innerHTML;
}

// Add helper functions for sessionStorage at the top of the file
function getMemberData() {
  try {
    return JSON.parse(sessionStorage.getItem('memberData'));
  } catch (error) {
    return null;
  }
}

function setMemberData(data) {
  try {
    if (data) {
      sessionStorage.setItem('memberData', JSON.stringify(data));

      // Switch to profile view if panel is visible
      if (window.isPanelVisible) {
        const contentSection = document.querySelector('.rewards-panel > div:last-child');
        if (contentSection) {
          const currentView = contentSection.firstElementChild;
          if (currentView) {
            const currentViewType = currentView.getAttribute('data-view');
            // Only switch to profile view if we're in registration, loading, or already in profile view
            if (currentViewType === 'profile' || currentViewType === 'registration' || currentViewType === 'loading') {
              renderView(
                profileView(data, getTiers(), window.shopData.pointsName,getFreeShippingEligible()),
                contentSection
              );
            }
          }
        }
      }

      // Hide popup since user is now a member
      hideRewardsPopup();
    } else {
      sessionStorage.removeItem('memberData');

      // Only switch to registration view if we don't have member data and user is logged in
      if (window.isPanelVisible && window.customerData?.id) {
        const contentSection = document.querySelector('.rewards-panel > div:last-child');
        if (contentSection) {
          const currentView = contentSection.firstElementChild;
          const currentViewType = currentView?.getAttribute('data-view');

          // Only show registration view if we're not in the middle of OTP verification
          if (currentViewType !== 'otp-verification') {
            renderView(registrationView(), contentSection);
          }
        }
      }

      // Show popup if there are items in cart and user is not logged in
      if (!window.customerData?.id) {
        showRewardsPopup();
      }
    }
  } catch (error) {
    console.error('Error setting member data:', error);
  }
}

// Update toggleRewardsPanel function
export function toggleRewardsPanel() {
  const panel = document.getElementById('rewards-panel');
  const popup = document.getElementById('rewards-popup');
  const buttonIcon = document.getElementById('button-icon');
  const buttonText = document.getElementById('button-text');
  const button = document.getElementById('rewards-button');
  const isVisible = panel.style.transform === 'translateY(0%)';

  if (isVisible) {
    panel.style.transform = 'translateY(100%)';
    panel.style.opacity = '0';
    panel.style.pointerEvents = 'none';
    buttonIcon.innerHTML = renderIconToString(ICONS.HEART);
    buttonText.style.display = 'inline';
    button.style.width = '';
    button.style.padding = '8px 16px';
    button.style.height = '';
    window.isPanelVisible = false;
    stopAutoRefresh();

    // Show popup again if conditions are met
    if (!getMemberData()) {
      setTimeout(() => {
        showRewardsPopup();
      }, 500);
    }

    if (customerDataPoller) {
      clearInterval(customerDataPoller);
      customerDataPoller = null;
    }
  } else {
    panel.style.transform = 'translateY(0%)';
    panel.style.opacity = '1';
    panel.style.pointerEvents = 'auto';
    buttonIcon.innerHTML = renderIconToString(ICONS.CLOSE);
    buttonText.style.display = 'none';
    button.style.width = '36px';
    button.style.padding = '8px';
    button.style.height = '36px';
    window.isPanelVisible = true;

    // Hide popup when panel opens
    hideRewardsPopup();

    // Show loading view immediately
    const contentSection = document.querySelector('.rewards-panel > div:last-child');
    renderView(loadingView(), contentSection);

    if (window.customerData) {
      loadWidgetData().then(() => {
        initializeWidget();
        startAutoRefresh();
      });
    } else {
      startCustomerDataPolling();
    }
  }
}

export function onlyNumbers(event) {
  const charCode = (event.which) ? event.which : event.keyCode;
  if (charCode > 31 && (charCode < 48 || charCode > 57)) {
    return false;
  }
  return true;
}

export function formatPhoneNumber(input) {
  let number = input.value.replace(/\D/g, '');

  // Remove leading zero if present
  if (number.startsWith('0')) {
    number = number.substring(1);
  }

  // Format the number
  if (number.length > 0) {
    if (number.length <= 2) {
      input.value = '0' + number;
    } else if (number.length <= 5) {
      input.value = '0' + number.substring(0, 2) + ' ' + number.substring(2);
    } else if (number.length <= 9) {
      input.value = '0' + number.substring(0, 2) + ' ' + number.substring(2, 5) + ' ' + number.substring(5);
    }
  }

  validatePhoneNumber(input.value);
}

export function validatePhoneNumber(number) {
  const phoneError = document.getElementById('phone-error');
  const submitButton = document.querySelector('button');

  // Remove all non-digits
  const digits = number.replace(/\D/g, '');

  // Check if it's a valid Sri Lankan mobile number
  const isValid = /^0[0-9]{9}$/.test(digits) || /^[0-9]{9}$/.test(digits);

  if (!isValid && digits.length > 0 || digits.length === 0) {
    phoneError.style.display = 'block';
    phoneError.textContent = digits.length === 0 ? 'Please enter your mobile number' : 'Please enter a valid mobile number';
    submitButton.disabled = true;
    submitButton.style.opacity = '0.7';
  } else {
    phoneError.style.display = 'none';
    submitButton.disabled = false;
    submitButton.style.opacity = '1';
  }

  return isValid;
}

export function showEarnPoints() {
  const contentSection = document.querySelector('.rewards-panel > div:last-child');
  fadeView(earnPointsView(getGeneralSpendingRule()), contentSection);
}

async function registerMemberWithOtp(mobileNumber, customerId) {
  const response = await fetch('/apps/members', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      customerId: customerId,
      mobileNumber: mobileNumber ? mobileNumber.replace('+', '') : '',
      action: 'registerWithOtpRequest',
      primaryKey: PRIMARY_KEY,
    })
  });

  const data = await response.json();

  if (!response.ok || data.error) {
    // Handle case where error is an array of objects with message field
    if (Array.isArray(data.error) && data.error.length > 0 && data.error[0].message) {
      throw new Error(data.error[0].message);
    } else {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }
  }

  return data;
}
export async function validateAndSubmit() {
  const phoneInput = document.getElementById('phone');
  const number = phoneInput.value.replace(/\D/g, '');
  const phoneError = document.getElementById('phone-error');
  const submitButton = document.getElementById('register-btn');

  // Check if phone number is empty
  if (!number) {
    phoneError.style.display = 'block';
    phoneError.textContent = 'Please enter your mobile number';
    return;
  }

  if (validatePhoneNumber(number)) {
    // Disable button and show loading state
    submitButton.disabled = true;
    const originalText = submitButton.textContent;
    submitButton.innerHTML = `
      <div style="display: inline-flex; align-items: center; gap: 8px;">
        <div style="
          width: 16px;
          height: 16px;
          border: 2px solid #ffffff;
          border-top: 2px solid transparent;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        "></div>
        <span>Sending...</span>
      </div>
    `;

    // Format number to standard format (always with leading zero)
    let standardNumber = number;
    if (standardNumber.startsWith('0')) {
      // Replace leading 0 with 94
      standardNumber = '94' + standardNumber.substring(1);
    }

    try {
      const result = await registerMemberWithOtp(standardNumber, window.customerData.id);
      showOTPVerificationView(number, result.registerMemberToken);
    } catch (error) {
      console.error('Error registering member:', error);
      phoneError.style.display = 'block';
      phoneError.textContent = error.message || 'Failed to send OTP. Please try again.';

      // Reset button state
      submitButton.disabled = false;
      submitButton.innerHTML = originalText;
    }
  }
}



export function showNotification(message, type = 'success', duration = 3000) {
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${type === 'success' ? 'green' : '#dc3545'};
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-family: var(--rewards-font-family);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 999999;
    animation: slideIn 0.3s ease;
    max-width: 300px;
  `;
  notification.textContent = message;
  document.body.appendChild(notification);

  setTimeout(() => {
    notification.style.animation = 'slideOut 0.3s ease';
    setTimeout(() => notification.remove(), 300);
  }, duration);
}

export async function resendOTP(mobileNumber) {
  const resendButton = document.getElementById('resend-btn');

  // Disable resend button and change text
  if (resendButton) {
    resendButton.style.pointerEvents = 'none';
    resendButton.style.opacity = '0.5';
    const originalText = resendButton.textContent;
    resendButton.textContent = 'Sending...';
  }

  try {
    showNotification('Sending...');
    const result = await registerMemberWithOtp(mobileNumber, window.customerData.id);
    showOTPVerificationView(mobileNumber, result.registerMemberToken);
    showNotification('Verification code sent successfully');

    // Note: We don't need to re-enable the button here because showOTPVerificationView
    // recreates the entire view, including a new resend button
  } catch (error) {
    console.error("Failed to resend the otp:", error);
    showNotification(error.message || 'Failed to send verification code', 'error');

    // Re-enable resend button if there's an error
    if (resendButton) {
      resendButton.style.pointerEvents = 'auto';
      resendButton.style.opacity = '1';
      resendButton.textContent = 'Resend';
    }
  }
}

function showOTPVerificationView(phoneNumber, registerMemberToken) {
  const step2Content = document.querySelector('[data-step="2"]');
  // Add data attribute to identify OTP verification state
  step2Content.setAttribute('data-view', 'otp-verification');
  step2Content.innerHTML = `
      <div style="
        position: absolute;
        top: -12px;
        left: 20px;
        background: white;
        padding: 0 10px;
        color: var(--rewards-text-color);
        font-size: 18px;
      ">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" style="display: inline-block; vertical-align: middle; margin-right: 5px;">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" fill="#C87F95"/>
        </svg>
        Step: 2
      </div>
      <h4 style="
        margin: 0 0 8px 0;
        font-size: 20px;
        color: #fff;
        font-family: var(--rewards-font-family);
      ">Welcome to ${window.shopData.name} Rewards!</h4>

      <p style="
        margin: 0 0 15px 0;
        color: #666;
        font-size: 14px;
        line-height: 1.3;
      ">We have sent a verification code to: ${phoneNumber}</p>

      <div style="
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-bottom: 20px;
      ">
        <input type="text" maxlength="1" class="otp-input" style="
          width: 50px;
          height: 50px;
          text-align: center;
          font-size: 20px;
          line-height: 50px;
          padding: 0;
          border: 1px solid #ddd;
          border-radius: 8px;
          font-family: var(--rewards-font-family);
          box-sizing: border-box;
        ">
        <input type="text" maxlength="1" class="otp-input" style="
          width: 50px;
          height: 50px;
          text-align: center;
          font-size: 20px;
          border: 1px solid #ddd;
          border-radius: 8px;
          font-family: var(--rewards-font-family);
        ">
        <input type="text" maxlength="1" class="otp-input" style="
          width: 50px;
          height: 50px;
          text-align: center;
          font-size: 20px;
          border: 1px solid #ddd;
          border-radius: 8px;
          font-family: var(--rewards-font-family);
        ">
        <input type="text" maxlength="1" class="otp-input" style="
          width: 50px;
          height: 50px;
          text-align: center;
          font-size: 20px;
          border: 1px solid #ddd;
          border-radius: 8px;
          font-family: var(--rewards-font-family);
        ">
        <input type="text" maxlength="1" class="otp-input" style="
          width: 50px;
          height: 50px;
          text-align: center;
          font-size: 20px;
          border: 1px solid #ddd;
          border-radius: 8px;
          font-family: var(--rewards-font-family);
        ">
      </div>

      <p id="otp-error" style="
        color: #dc3545;
        font-size: 12px;
        margin: -10px 0 15px 0;
        display: none;
        text-align: center;
      "></p>

      <button
        id="verify-btn"
        onclick="window.verifyOTP('${registerMemberToken}')"
        style="
          width: 100%;
          padding: 12px;
          background: var(--rewards-primary-color);
          color: white;
          border: none;
          border-radius: 100px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          font-family: var(--rewards-font-family);
      ">Verify</button>

      <p style="
        margin: 15px 0 0 0;
        text-align: center;
      ">
        Didn't receive a code?
        <a href="#"
          id="resend-btn"
          onclick="window.resendOTP('${phoneNumber}')"
          style="
            color: var(--rewards-text-color);
            text-decoration: none;
            font-weight: 500;
          ">Resend</a>
      </p>
    `;

  // Add OTP input behavior
  setupOTPInputs();
}

const aboutView = () => html`
  ${headerWithBackButton(`About ${window.shopData.programName}`, () => showProfileView())}

  <div style="
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  ">
    <h4 style="
      margin: 0 0 20px 0;
      font-size: 24px;
      color: var(--rewards-text-color);
      font-family: var(--rewards-font-family);
    ">Welcome to ${window.shopData.programName}!</h4>

    <div style="
      color: var(--rewards-text-color);
      font-size: 16px;
      line-height: 1.6;
      font-family: var(--rewards-font-family);
    ">
      <p style="margin: 0 0 15px 0;">
        Our exclusive rewards program is designed to enhance your shopping experience!
      </p>

      <p style="margin: 0 0 15px 0;">
        As a member, you'll enjoy these amazing benefits:
      </p>

      <ul style="
        margin: 0 0 15px 0;
        padding-left: 20px;
      ">
        <li style="margin-bottom: 10px">
          Receive special birthday rewards
        </li>
        <li style="margin-bottom: 10px">
          Unlock exclusive benefits as you progress through our tiers
        </li>
        <li style="margin-bottom: 10px">
          Get early access to sales and special events
        </li>
      </ul>

      <p style="margin: 0;">
        Join us today and start your journey towards exclusive rewards and magical experiences!
      </p>
    </div>
  </div>
`;

// Update the showAbout function to use fadeView
export function showAbout() {
  const contentSection = document.querySelector('.rewards-panel > div:last-child');
  fadeView(aboutView(), contentSection);
}

// Make sure to export the function
window.showAbout = showAbout;

function setupOTPInputs() {
  const inputs = document.querySelectorAll('.otp-input');
  const otpError = document.getElementById('otp-error');

  inputs.forEach((input, index) => {
    input.addEventListener('keyup', (e) => {
      if (e.key >= 0 && e.key <= 9) {
        if (index < inputs.length - 1) {
          inputs[index + 1].focus();
        }
        // Hide error when user starts typing
        otpError.style.display = 'none';
      } else if (e.key === 'Backspace') {
        if (index > 0) {
          inputs[index - 1].focus();
        }
      }
    });

    // Allow only numbers
    input.addEventListener('input', (e) => {
      e.target.value = e.target.value.replace(/[^0-9]/g, '');
    });
  });
}

export async function verifyOTP(registerMemberToken) {
  const inputs = document.querySelectorAll('.otp-input');
  const code = Array.from(inputs).map(input => input.value).join('');
  const otpError = document.getElementById('otp-error');
  const verifyButton = document.getElementById('verify-btn');

  if (code.length === 5) {
    otpError.style.display = 'none';

    // Disable button and show loading state
    verifyButton.disabled = true;
    const originalText = verifyButton.textContent;
    verifyButton.innerHTML = `
      <div style="display: inline-flex; align-items: center; gap: 8px;">
        <div style="
          width: 16px;
          height: 16px;
          border: 2px solid #ffffff;
          border-top: 2px solid transparent;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        "></div>
        <span>Verifying...</span>
      </div>
    `;

    try {
      const response = await fetch('/apps/members', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          registerMemberToken,
          otpCode: code,
          action: 'registerWithOtp'
        })
      });
      const memberData = await response.json();

      if (memberData.error) {
        console.error('OTP verification failed:', memberData.error);
        otpError.style.display = 'block';

        // Handle case where error is an array of objects with message field
        if (Array.isArray(memberData.error) && memberData.error.length > 0 && memberData.error[0].message) {
          otpError.textContent = memberData.error[0].message;
        } else {
          otpError.textContent = memberData.error;
        }

        // Reset button state
        verifyButton.disabled = false;
        verifyButton.innerHTML = originalText;
      } else {
        // Store member data immediately
        setMemberData(memberData);
        showVerificationSuccess(memberData);

        // Instead of reloading the page, refresh the widget data and update the view
        await refreshMemberDataAfterOTP(memberData);
      }
    } catch (error) {
      console.error('OTP verification failed:', error);
      otpError.style.display = 'block';
      otpError.textContent = 'Invalid verification code. Please try again.';

      // Reset button state
      verifyButton.disabled = false;
      verifyButton.innerHTML = originalText;
    }

  } else {
    otpError.style.display = 'block';
    otpError.textContent = 'Please enter the complete verification code';
  }
}

// Add function to refresh member data after OTP verification
async function refreshMemberDataAfterOTP(memberData) {
  try {
    // Wait a moment for the backend to process the verification
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Force refresh the member data from the server
    await initializeInBackground();

    // If panel is visible, switch to profile view after a short delay
    if (window.isPanelVisible) {
      setTimeout(() => {
        const contentSection = document.querySelector('.rewards-panel > div:last-child');
        if (contentSection) {
          renderView(
            profileView(getMemberData(), getTiers(), window.shopData.pointsName, getFreeShippingEligible()),
            contentSection
          );
        }
      }, 2000); // Wait 2 seconds to show success message first
    }

    // Start the auto-refresh interval
    startMemberDataRefresh();

  } catch (error) {
    console.error('Error refreshing member data after OTP:', error);
    // Fallback to page reload if refresh fails
    setTimeout(() => {
      window.location.reload();
    }, 3000);
  }
}

console.log(window?.customerData);
export function showVerificationSuccess(memberData) {
  const step2Content = document.querySelector('[data-step="2"]');
  step2Content.innerHTML = `
      <div style="
        text-align: center;
        padding: 20px 0;
        opacity: 1;
        transition: opacity 0.3s ease;
      ">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" style="margin-bottom: 15px;">
          <circle cx="12" cy="12" r="12" fill="var(--rewards-primary-color)"/>
          <path d="M9.5 15.5L5.5 11.5L4 13L9.5 18.5L20.5 7.5L19 6L9.5 15.5Z" fill="white"/>
        </svg>
        <h4 style="
          margin: 0;
          font-size: 20px;
          color: var(--rewards-text-color);
          font-family: var(--rewards-font-family);
        ">Welcome to your ${window.shopData.name} account!</h4>
      </div>
    `;

  // Fade out after 1.7 seconds
  setTimeout(() => {
    const successDiv = step2Content.querySelector('div');
    successDiv.style.opacity = '0';
  }, 1700);

  // Show profile view after fade out (2 seconds total)
  setTimeout(() => {
    const contentSection = document.querySelector('.rewards-panel > div:last-child');
    contentSection.scrollTop = 0;
    showProfileView();
  }, 2000);
}

function getRGBValues(hex) {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `${r}, ${g}, ${b}`;
}

function getCurrentTierAndNextTier(tiers = [], memberData) {
  let currentTier = null;
  let nextTier = null;
  const sortedTiers = tiers.sort((a, b) => a.points - b.points);
  if (memberData?.tier?.tierId) {
    currentTier = tiers?.find(tier => tier._id === memberData.tier.tierId);
  } else {
    const tierPoints = memberData?.tierPoints ?? 0;
    // Use findLast to get the highest qualifying tier
    currentTier = sortedTiers.findLast(tier => tier.points <= tierPoints);
  }

  if (currentTier) {
    const currentTierIndex = sortedTiers.findIndex(tier => tier._id === currentTier._id);
    nextTier = sortedTiers.length > currentTierIndex + 1 ? sortedTiers[currentTierIndex + 1] : null;
  }
  return { currentTier, nextTier };
}

export function showProfileView() {
  const primaryColor = window.shopData.theme.primaryColor;
  const rgbValues = getRGBValues(primaryColor);
  const pointsName = window.shopData.pointsName;
  const { currentTier, nextTier } = getCurrentTierAndNextTier(getTiers(), getMemberData());

  const contentSection = document.querySelector('.rewards-panel > div:last-child');
  renderView(
    profileView(getMemberData(), getTiers(), pointsName,getFreeShippingEligible()),
    contentSection
  );
}

export function showRedeemPoints() {
  const contentSection = document.querySelector('.rewards-panel > div:last-child');
  const availablePoints = getMemberData()?.allowedRedeemablePoints || 0;
  fadeView(redeemPointsView(availablePoints), contentSection);
}

// Add this function to handle redemption
export async function handleRedemption(points) {
  const contentSection = document.querySelector('.rewards-panel > div:last-child');
  const redeemButton = document.getElementById('redeem-points-button');
  const memberId = getMemberData()?._id;
  const customerId = window.customerData?.id;

  if (!redeemButton) {
    console.error('Redeem button not found');
    return;
  }
  if (!memberId||!customerId) {
    console.error('Customer ID not found');
    alert('Customer information not available. Please try again later.');
    return;
  }

  // Store the original button content
  const originalContent = redeemButton.innerHTML;

  try {
    // Update button to loading state
    render(html`
      <div style="display: inline-flex; align-items: center; gap: 8px;">
        <div style="
          width: 20px;
          height: 20px;
          border: 2px solid #ffffff;
          border-top: 2px solid transparent;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        "></div>
        <span>Processing...</span>
      </div>
    `, redeemButton);

    // Add keyframe animation for spinner if not already added
    if (!document.getElementById('spin-animation')) {
      const style = document.createElement('style');
      style.id = 'spin-animation';
      style.textContent = `
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `;
      document.head.appendChild(style);
    }

    // Disable button while processing
    redeemButton.disabled = true;
    redeemButton.style.cursor = 'not-allowed';
    redeemButton.style.opacity = '0.7';

    const response = await fetch('/apps/members', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        pointsAmount: points,
        shopId: window.shopData.id,
        action: 'redeemPoints',
        memberId: memberId,
        customerId:customerId,
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const { discountCode } = await response.json();

    // Show success view
    contentSection.style.opacity = '0';
    setTimeout(() => {
      renderView(successView(discountCode, points), contentSection);
      setTimeout(() => contentSection.style.opacity = '1', 50);
    }, 300);

  } catch (error) {
    console.error('Error during redemption:', error);

    // Restore button to original state
    redeemButton.disabled = false;
    redeemButton.style.cursor = 'pointer';
    redeemButton.style.opacity = '1';
    redeemButton.innerHTML = originalContent;

    alert('Failed to redeem points. Please try again.');
  }
}

const successView = (discountCode, points) => html`
  <div data-view="success" style="
    text-align: center;
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
  ">
    <div style="
      width: 64px;
      height: 64px;
      border-radius: 50%;
      background: var(--rewards-primary-color);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      padding: 20px;
      box-sizing: border-box;
    ">
      <svg width="40" height="40" viewBox="0 0 24 24" fill="none" style="color: white;">
        <path d="M9.5 15.5L5.5 11.5L4 13L9.5 18.5L20.5 7.5L19 6L9.5 15.5Z" fill="currentColor"/>
      </svg>
    </div>

    <h3 style="
      margin: 0;
      font-size: 24px;
      color: var(--rewards-text-color);
      font-family: var(--rewards-font-family);
    ">Points Redeemed Successfully!</h3>

    <p style="
      margin: 0;
      color: var(--rewards-text-color);
      font-size: 16px;
      line-height: 1.5;
      font-family: var(--rewards-font-family);
    ">
      You've redeemed ${points} ${window.shopData.pointsName}
    </p>

    <div style="
      background: #F3F4F6;
      padding: 15px;
      border-radius: 12px;
      width: 100%;
    ">
      <div style="
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
        margin-bottom: 10px;
      ">
        <span style="
          font-size: 16px;
          color: var(--rewards-text-color);
          font-family: var(--rewards-font-family);
        ">${discountCode}</span>

        <button
          @click=${() => {
    navigator.clipboard.writeText(discountCode).then(() => {
      const button = document.querySelector('[data-copy-button]');
      if (button) {
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        setTimeout(() => {
          button.textContent = originalText;
        }, 2000);
      }
    });
  }}
          data-copy-button
          style="
            background: none;
            border: 1px solid var(--rewards-primary-color);
            color: var(--rewards-primary-color);
            padding: 8px 16px;
            border-radius: 100px;
            cursor: pointer;
            font-family: var(--rewards-font-family);
            font-size: 14px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
          "
          onmouseover="this.style.background='rgba(var(--rewards-primary-color-rgb), 0.1)'"
          onmouseout="this.style.background='none'"
        >
          ${ICONS.COPY}
          Copy Code
        </button>
      </div>

      <button
        @click=${() => applyDiscountCode(discountCode)}
        style="
          background: var(--rewards-primary-color);
          color: white;
          border: none;
          padding: 12px;
          border-radius: 100px;
          cursor: pointer;
          font-family: var(--rewards-font-family);
          font-size: 14px;
          transition: opacity 0.2s ease;
          width: 100%;
        "
        onmouseover="this.style.opacity='0.9'"
        onmouseout="this.style.opacity='1'"
      >
        Apply Code
      </button>
    </div>

    <button
      @click=${() => showProfileView()}
      style="
        background: none;
        border: none;
        color: var(--rewards-primary-color);
        font-size: 16px;
        cursor: pointer;
        padding: 0;
        font-family: var(--rewards-font-family);
        text-decoration: underline;
      "
    >
      Back to Profile
    </button>
  </div>
`;

// Update the applyDiscountCode function
export function applyDiscountCode(code) {
  // Close the rewards panel
  const panel = document.getElementById('rewards-panel');
  const buttonIcon = document.getElementById('button-icon');
  const buttonText = document.getElementById('button-text');
  const button = document.getElementById('rewards-button');

  panel.style.transform = 'translateY(100%)';
  panel.style.opacity = '0';
  panel.style.pointerEvents = 'none';
  buttonIcon.innerHTML = renderIconToString(ICONS.HEART);
  buttonText.style.display = 'inline';
  button.style.width = '';
  button.style.padding = '8px 16px';
  button.style.height = '';
  window.isPanelVisible = false;

  // Save the cart redirect intention
  sessionStorage.setItem("redirect_to_cart", "true");

  // Navigate to apply the discount
  window.location.href = `/discount/${code}`;
}

// Make sure to export the function
window.applyDiscountCode = applyDiscountCode;

// Add a loading state flag
window.isWidgetDataLoading = false;

export async function loadWidgetData() {
  if (window.isWidgetDataLoading) return;
  if (isInitialized) {
    // If already initialized, just show popup
    showRewardsPopup();
    startMemberDataRefresh();
    return;
  }

  window.isWidgetDataLoading = true;
  try {
    startMemberDataRefresh();

    // Re-render the current view with new data if panel is visible
    if (window.isPanelVisible) {
      const contentSection = document.querySelector('.rewards-panel > div:last-child');
      if (contentSection) {
        const currentView = contentSection.firstElementChild;
        if (currentView) {
          const template = currentView.getAttribute('data-view');
          switch (template) {
            case 'profile':
              render(profileView(getMemberData(), getTiers(), window.shopData.pointsName,getFreeShippingEligible()), contentSection);
              break;
          }
        }
      }
    }
  } catch (error) {
    console.error('Error loading widget data:', error);
  } finally {
    window.isWidgetDataLoading = false;
  }
}

export async function initializeWidget() {
  if (window.customerData) {
    const memberData = getMemberData();

    // If we have member data, show profile view
    if (memberData) {
      setMemberData(memberData); // This will handle the view switching
      // Load coupons in background if we have member data
      if (memberData?.cardNumber) {
        loadCouponsInBackground();
      }
    } else {
      // If no member data but user is logged in, try to fetch from server
      try {
        await initializeInBackground();
      } catch (error) {
        console.error('Error initializing widget:', error);
        // If fetch fails and no member data, show registration view
        setMemberData(null);
      }
    }
  } else {
    // User not logged in, clear any stored member data
    setMemberData(null);
  }
}

export function showTierBenefits() {
  const contentSection = document.querySelector('.rewards-panel > div:last-child');
  const { currentTier } = getCurrentTierAndNextTier(getTiers(), getMemberData());
  fadeView(tierBenefitsView(getTiers(), currentTier), contentSection);
}

// Helper function for view transitions
function fadeView(view, container) {
  container.style.opacity = '0';
  setTimeout(() => {
    container.scrollTop = 0;
    renderView(view, container);
    setTimeout(() => container.style.opacity = '1', 50);
  }, 300);
}

// Add cleanup to the unload handler in widget.liquid
window.addEventListener('unload', () => {
  stopAutoRefresh();
  stopMemberDataRefresh();
  if (customerDataPoller) {
    clearInterval(customerDataPoller);
    customerDataPoller = null;
  }
});

// Make sure all other functions are also properly exported to window
window.showTierBenefits = showTierBenefits;
window.showProfileView = showProfileView;
window.showRedeemPoints = showRedeemPoints;
window.showAbout = showAbout;
window.handleRedemption = handleRedemption;
window.applyDiscountCode = applyDiscountCode;
window.loadWidgetData = loadWidgetData;
window.initializeWidget = initializeWidget;

// Add these new functions
export function calculatePotentialPoints() {
  const cartTotal = window.Shopify?.cart?.total_price
    ? window.Shopify.cart.total_price / 100
    : 0;

  const pointConfig = getPointConfiguration();
  const pointsPerUnit = 1 / (pointConfig.pointsEarnedRatio || 100);
  const potentialPoints = Math.floor(cartTotal * pointsPerUnit);
  return potentialPoints;
}

export function showRewardsPopup() {
  if (popupVisible || window.isPanelVisible) {
    return;
  }

  const popup = document.getElementById('rewards-popup');
  const potentialPointsSpan = document.getElementById('potential-points');

  // Show popup if user is not a loyalty member and has items in cart
  if (!getMemberData() && window.Shopify?.cart?.item_count > 0) {
    const points = calculatePotentialPoints();
    if (points > 0) {
      potentialPointsSpan.textContent = points;
      popup.style.transform = 'translateY(0)';
      popup.style.opacity = '1';
      popup.style.pointerEvents = 'auto';
      popupVisible = true;
    }
  }
}

export function hideRewardsPopup() {
  const popup = document.getElementById('rewards-popup');
  popup.style.transform = 'translateY(100%)';
  popup.style.opacity = '0';
  popup.style.pointerEvents = 'none';
  popupVisible = false;
}

// Make sure to export the new functions
window.showRewardsPopup = showRewardsPopup;
window.hideRewardsPopup = hideRewardsPopup;
// Add a new function for background initialization
export async function initializeInBackground() {
  try {
    // Get mobile number from multiple sources for better reliability
    const getMobileNumber = () => {
      // First try window.customerData.phone
      if (window.customerData?.phone) {
        return window.customerData.phone.replace('+', '');
      }

      // Then try from stored member data
      const storedMemberData = getMemberData();
      if (storedMemberData?.mobileNumber) {
        return storedMemberData.mobileNumber;
      }

      // Return empty string as fallback
      return "";
    };

    const params = new URLSearchParams({
      primaryKey: PRIMARY_KEY,
      ...(PRIMARY_KEY !== "MOBILE_NUMBER" ? {
        logged_in_customer_id: window.customerData.id
      } : {
        mobileNumber: getMobileNumber()
      }),
    });

    const response = await fetch(`/apps/members?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (response.status === 401) {
      // Unauthorized - user might be logged out
      clearSessionData();
      return;
    }

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const { member = null, generalSpendingRule={},freeShippingEligible=false,tiers = [], pointConfiguration = {
      minPointRedemptionAmount: 1,
      maxPointRedemptionAmount: 1000000,
      minPointsBalanceForRedemption: 0,
      currencyAmountPerPoint: 1,
      pointsEarnedRatio: 100
    } } = await response.json();

    // Update sessionStorage instead of window
    setTiers(tiers);
    setPointConfiguration(pointConfiguration);
    isInitialized = true;
    // This will handle view switching and popup visibility
    setMemberData(member);
    setGeneralSpendingRule(generalSpendingRule);
    setFreeShippingEligible(freeShippingEligible);
  } catch (error) {
    console.error('Error initializing widget in background:', error);
  }
}

// Add functions to manage the refresh interval
export function startMemberDataRefresh() {
  // Remove the condition that prevents refresh for members
  if (!memberDataRefreshInterval && window.customerData?.id) {
    memberDataRefreshInterval = setInterval(async () => {
      try {
        await initializeInBackground();
      } catch (error) {
        console.error('Error during member data refresh:', error);
        // If refresh fails multiple times, we might need to clear invalid data
        // This prevents the widget from getting stuck in a bad state
      }
    }, 15000); // 15 seconds
  }
}

export function stopMemberDataRefresh() {
  if (memberDataRefreshInterval) {
    clearInterval(memberDataRefreshInterval);
    memberDataRefreshInterval = null;
  }
}

// Update the cleanup handler
window.addEventListener('unload', () => {
  stopAutoRefresh();
  stopMemberDataRefresh();
  if (customerDataPoller) {
    clearInterval(customerDataPoller);
    customerDataPoller = null;
  }
});


// Export new functions
window.startMemberDataRefresh = startMemberDataRefresh;
window.stopMemberDataRefresh = stopMemberDataRefresh;

// Add new helper functions for tiers and pointConfiguration
function getTiers() {
  try {
    return JSON.parse(sessionStorage.getItem('tiers')) || [];
  } catch (error) {
    return [];
  }
}

function setTiers(tiers) {
  try {
    sessionStorage.setItem('tiers', JSON.stringify(tiers));
  } catch (error) {
    console.error('Error setting tiers:', error);
  }
}

function getPointConfiguration() {
  try {
    return JSON.parse(sessionStorage.getItem('pointConfiguration')) || {
      minPointRedemptionAmount: 1,
      maxPointRedemptionAmount: 1000000,
      minPointsBalanceForRedemption: 0,
      currencyAmountPerPoint: 100,
      pointsEarnedRatio: 100
    };
  } catch (error) {
    return {
      minPointRedemptionAmount: 1,
      maxPointRedemptionAmount: 1000000,
      minPointsBalanceForRedemption: 0,
      currencyAmountPerPoint: 100,
      pointsEarnedRatio: 100
    };
  }
}

function setPointConfiguration(config) {
  try {
    sessionStorage.setItem('pointConfiguration', JSON.stringify(config));
  } catch (error) {
    console.error('Error setting point configuration:', error);
  }
}

function setFreeShippingEligible(value) {
  try {
    sessionStorage.setItem('freeShippingEligible', JSON.stringify(value));
  } catch (error) {
    console.error('Error setting free shipping eligible:', error);
  }
}

function getFreeShippingEligible() {
  try {
    const value = sessionStorage.getItem('freeShippingEligible');
    return value !== null ? JSON.parse(value) : false;
  } catch (error) {
    console.error('Error getting free shipping eligible:', error);
    return false;
  }
}

window.setFreeShippingEligible = setFreeShippingEligible;
window.getFreeShippingEligible = getFreeShippingEligible;

function setGeneralSpendingRule(value) {
  try {
    sessionStorage.setItem('generalSpendingRule', JSON.stringify(value));
  } catch (error) {
    console.error('Error setting general spending rule:', error);
  }
}

function getGeneralSpendingRule() {
  try {
    const value = sessionStorage.getItem('generalSpendingRule');
    return value !== null ? JSON.parse(value) : false;
  } catch (error) {
    console.error('Error getting general spending rule:', error);
    return false;
  }
}

window.setGeneralSpendingRule = setGeneralSpendingRule;
window.getGeneralSpendingRule = getGeneralSpendingRule;

// Add a new function to clear all session data
function clearSessionData() {
  try {
    sessionStorage.removeItem('memberData');
    sessionStorage.removeItem('tiers');
    sessionStorage.removeItem('pointConfiguration');
    sessionStorage.removeItem('freeShippingEligible');
    isInitialized = false;
    stopMemberDataRefresh();
  } catch (error) {
    console.error('Error clearing session data:', error);
  }
}

// Update startCustomerDataPolling to check for logout
function startCustomerDataPolling() {
  if (customerDataPoller) return;

  let previousCustomerId = window.customerData?.id;

  customerDataPoller = setInterval(() => {
    // If customer ID changes or becomes null, clear session data
    if (window.customerData?.id !== previousCustomerId) {
      if (!window.customerData?.id) {
        // Customer logged out
        clearSessionData();
      }
      previousCustomerId = window.customerData?.id;
    }
  }, 1000); // Check every second
}

// Update the fetchCouponHistory function to use Shopify's endpoint
async function fetchCouponHistory() {
  try {

    // Then get discount codes from Shopify
    const shopifyResponse = await fetch('/apps/members/discount-codes', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!shopifyResponse.ok) {
      throw new Error(`HTTP error! status: ${shopifyResponse.status}`);
    }

    const discountCodes = await shopifyResponse.json();


    return discountCodes ?? [];
  } catch (error) {
    console.error('Error fetching coupon history:', error);
    return [];
  }
}

// Update the couponHistoryView to show sections
const couponHistoryView = (coupons = [],freeShippingEligible=false) => {
  const memberId = getMemberData()?._id;
  const cardNumber = window.customerData?.id;

  if (!memberId||!cardNumber) {
    console.error('Customer ID not found');
    showNotification('Customer information not available. Please try again later', 'error');
    return;
  }
  async function generateFreeShippingCoupon() {
    try {
      const response = await fetch('/apps/members', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generateFreeShippingCoupon',
          customerId: cardNumber,
          memberId:memberId
        })
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      showNotification('Free shipping coupon generated successfully!', 'success');
      const updatedCoupons = await fetchCouponHistory();
      const contentSection = document.querySelector('.rewards-panel > div:last-child');
      fadeView(couponHistoryView(updatedCoupons,getFreeShippingEligible()), contentSection);
    } catch (error) {
      console.error('Error generating free shipping coupon:', error);
      showNotification('Failed to generate free shipping coupon', 'error');
    }
  }

  const availableCoupons = coupons.filter(coupon =>
    coupon.status === 'ACTIVE' &&
    (!coupon.usageLimit || coupon.timesUsed < coupon.usageLimit) &&
    (!coupon.endsAt || new Date(coupon.endsAt) >= new Date())
  );

  const appliedCoupons = coupons.filter(coupon =>
    coupon.status === 'USED' ||
    coupon.timesUsed >= coupon.usageLimit ||
    (coupon.endsAt && new Date(coupon.endsAt) < new Date())
  );

  return html`
    ${headerWithBackButton('My Coupons', () => showProfileView())}

    <div style="display: flex; flex-direction: column; gap: 24px;" data-view="coupon-history">
      <!-- Free Shipping Coupons Section -->
      ${freeShippingEligible ? html`
        <div>
          <h3 style="
            margin: 0 0 16px 0;
            font-size: 20px;
            color: #333;
            font-family: var(--rewards-font-family);
          ">Free Shipping Coupons</h3>

          ${availableCoupons.some(coupon => coupon.title === 'FREE_SHIPPING_DISCOUNT') ? html`
            <div style="display: flex; flex-direction: column; gap: 15px;">
              ${availableCoupons.filter(coupon => coupon.title === 'FREE_SHIPPING_DISCOUNT').map(coupon => html`
                <div style="
                  background: white;
                  border-radius: 12px;
                  padding: 20px;
                  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                ">
                  <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 10px;
                  ">
                    <div>
                      <h4 style="
                        margin: 0 0 8px 0;
                        font-size: 18px;
                        color: #333;
                      ">${coupon.code}</h4>
                      <p style="
                        margin: 0 0 4px 0;
                        font-size: 14px;
                        color: #666;
                      ">Free Shipping</p>
                      <p style="
                        margin: 0 0 4px 0;
                        font-size: 12px;
                        color: #888;
                      ">Generated on ${new Date(coupon.startsAt).toLocaleDateString()}</p>
                      ${coupon.endsAt ? html`
                        <p style="
                          margin: 0;
                          font-size: 12px;
                          color: #888;
                        ">Expires on ${new Date(coupon.endsAt).toLocaleDateString()}</p>
                      ` : ''}
                    </div>
                    <div style="
                      padding: 4px 12px;
                      border-radius: 100px;
                      background: #DEF7EC;
                      color: #03543F;
                      font-size: 12px;
                    ">Active</div>
                  </div>

                  <div style="
                    display: flex;
                    gap: 10px;
                    margin-top: 15px;
                  ">
                    <button
                      @click=${() => {
            navigator.clipboard.writeText(coupon.code);
            showNotification('Coupon code copied!');
          }}
                      style="
                        flex: 1;
                        padding: 8px;
                        border: 1px solid var(--rewards-primary-color);
                        background: none;
                        color: var(--rewards-primary-color);
                        border-radius: 100px;
                        cursor: pointer;
                        font-family: var(--rewards-font-family);
                        transition: all 0.2s ease;
                      "
                      onmouseover="this.style.background='rgba(var(--rewards-primary-color-rgb), 0.1)'"
                      onmouseout="this.style.background='none'"
                    >Copy Code</button>
                    <button
                      @click=${() => applyDiscountCode(coupon.code)}
                      style="
                        flex: 1;
                        padding: 8px;
                        background: var(--rewards-primary-color);
                        color: white;
                        border: none;
                        border-radius: 100px;
                        cursor: pointer;
                        font-family: var(--rewards-font-family);
                        transition: opacity 0.2s ease;
                      "
                      onmouseover="this.style.opacity='0.9'"
                      onmouseout="this.style.opacity='1'"
                    >Apply Code</button>
                  </div>
                </div>
              `)}
            </div>
          ` : html`
            <div style="
              background: white;
              border-radius: 12px;
              padding: 20px;
              box-shadow: 0 4px 12px rgba(0,0,0,0.1);
              text-align: center;
            ">
              <p style="
                margin: 0 0 15px 0;
                font-size: 14px;
                color: #666;
                font-family: var(--rewards-font-family);
              ">No free shipping coupons yet</p>
              <button
                @click=${() => generateFreeShippingCoupon()}
                style="
                  padding: 10px 20px;
                  background: var(--rewards-primary-color);
                  color: white;
                  border: none;
                  border-radius: 100px;
                  cursor: pointer;
                  font-family: var(--rewards-font-family);
                  transition: opacity 0.2s ease;
                "
                onmouseover="this.style.opacity='0.9'"
                onmouseout="this.style.opacity='1'"
              >Generate Free Shipping Coupon</button>
            </div>
          `}
        </div>
      ` : ''}

      <!-- Available Coupons Section -->
      <div>
        <h3 style="
          margin: 0 0 16px 0;
          font-size: 20px;
          color: #333;
          font-family: var(--rewards-font-family);
        ">Available Coupons</h3>

        ${availableCoupons.length === 0 ? html`
          <div style="
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 12px;
            color: #666;
          ">
            No available coupons
          </div>
        ` : html`
          <div style="display: flex; flex-direction: column; gap: 15px;">
            ${availableCoupons.filter(coupon=>coupon.title!=="FREE_SHIPPING_DISCOUNT").map(coupon => html`
              <div style="
                background: white;
                border-radius: 12px;
                padding: 20px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
              ">
                <div style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 10px;
                ">
                  <div>
                    <h4 style="
                      margin: 0 0 8px 0;
                      font-size: 18px;
                      color: #333;
                    ">${coupon.code}</h4>
                    <p style="
                      margin: 0 0 4px 0;
                      font-size: 14px;
                      color: #666;
                    ">${window.shopData.currency} ${coupon.pointsRedeemed} off</p>
                    <p style="
                      margin: 0 0 4px 0;
                      font-size: 12px;
                      color: #888;
                    ">Bought on ${new Date(coupon.startsAt).toLocaleDateString()}</p>
                    ${coupon.endsAt ? html`
                      <p style="
                        margin: 0;
                        font-size: 12px;
                        color: #888;
                      ">Expires on ${new Date(coupon.endsAt).toLocaleDateString()}</p>
                    ` : ''}
                  </div>
                  <div style="
                    padding: 4px 12px;
                    border-radius: 100px;
                    background: #DEF7EC;
                    color: #03543F;
                    font-size: 12px;
                  ">Active</div>
                </div>

                <div style="
                  display: flex;
                  gap: 10px;
                  margin-top: 15px;
                ">
                  <button
                    @click=${() => {
                        navigator.clipboard.writeText(coupon.code);
                        showNotification('Coupon code copied!');
                      }}
                    style="
                      flex: 1;
                      padding: 8px;
                      border: 1px solid var(--rewards-primary-color);
                      background: none;
                      color: var(--rewards-primary-color);
                      border-radius: 100px;
                      cursor: pointer;
                      font-family: var(--rewards-font-family);
                      transition: all 0.2s ease;
                    "
                    onmouseover="this.style.background='rgba(var(--rewards-primary-color-rgb), 0.1)'"
                    onmouseout="this.style.background='none'"
                  >Copy Code</button>
                  <button
                    @click=${() => applyDiscountCode(coupon.code)}
                    style="
                      flex: 1;
                      padding: 8px;
                      background: var(--rewards-primary-color);
                      color: white;
                      border: none;
                      border-radius: 100px;
                      cursor: pointer;
                      font-family: var(--rewards-font-family);
                      transition: opacity 0.2s ease;
                    "
                    onmouseover="this.style.opacity='0.9'"
                    onmouseout="this.style.opacity='1'"
                  >Apply Code</button>
                </div>
              </div>
            `)}
          </div>
        `}
      </div>

      <!-- Applied Coupons Section -->
      <div>
        <h3 style="
          margin: 0 0 16px 0;
          font-size: 20px;
          color: #333;
          font-family: var(--rewards-font-family);
        ">Applied Coupons</h3>

        ${appliedCoupons.length === 0 ? html`
          <div style="
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 12px;
            color: #666;
          ">
            No applied coupons
          </div>
        ` : html`
          <div style="display: flex; flex-direction: column; gap: 15px;">
            ${appliedCoupons.map(coupon => html`
              <div style="
                background: white;
                border-radius: 12px;
                padding: 20px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
              ">
                <div style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                ">
                  <div>
                    <h4 style="
                      margin: 0 0 8px 0;
                      font-size: 18px;
                      color: #333;
                    ">${coupon.code}</h4>
                    <p style="
                      margin: 0 0 4px 0;
                      font-size: 14px;
                      color: #666;
                    ">${window.shopData.currency} ${coupon.pointsRedeemed} off</p>
                    <p style="
                      margin: 0;
                      font-size: 12px;
                      color: #888;
                    ">Bought on ${new Date(coupon.startsAt).toLocaleDateString()}</p>
                  </div>
                  <div style="
                    padding: 4px 12px;
                    border-radius: 100px;
                    background: #E5E7EB;
                    color: #374151;
                    font-size: 12px;
                  ">Used</div>
                </div>
              </div>
            `)}
          </div>
        `}
      </div>
    </div>
  `;
};

// Add this function to show coupon history
export function showCouponHistory() {
  const contentSection = document.querySelector('.rewards-panel > div:last-child');
  renderView(loadingView(), contentSection);

  // Fetch all coupons and show them
  fetchCouponHistory().then(coupons => {
    // Update available coupons cache
    availableCoupons = coupons.filter(coupon =>
      coupon.status === 'ACTIVE' &&
      (!coupon.usageLimit || coupon.timesUsed < coupon.usageLimit) &&
      (!coupon.endsAt || new Date(coupon.endsAt) >= new Date())
    );

    // Show all coupons in the view
    fadeView(couponHistoryView(coupons,getFreeShippingEligible()), contentSection);
  });
}

// Make sure to export the new function
window.showCouponHistory = showCouponHistory;


export function applyDiscountCodeSuccessCheck() {
  // Check if we need to redirect to the cart
  if (sessionStorage.getItem("redirect_to_cart")) {
    sessionStorage.removeItem("redirect_to_cart"); // Clean up
    window.location.href = "/cart"; // Redirect user to the cart
  }
}
